import 'package:flutter_test/flutter_test.dart';
import 'package:e_library/features/auth/data/models/auth_request.dart';
import 'package:e_library/features/auth/data/models/auth_response.dart';

void main() {
  group('Authentication Models', () {
    test('LoginRequest should serialize to JSON correctly', () {
      const request = LoginRequest(
        email: '<EMAIL>',
        password: 'password123',
      );

      final json = request.toJson();

      expect(json['email'], '<EMAIL>');
      expect(json['password'], 'password123');
    });

    test('LoginRequest should deserialize from JSON correctly', () {
      final json = {
        'email': '<EMAIL>',
        'password': 'password123',
      };

      final request = LoginRequest.fromJson(json);

      expect(request.email, '<EMAIL>');
      expect(request.password, 'password123');
    });

    test('RegisterRequest should serialize to JSON correctly', () {
      const request = RegisterRequest(
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123',
        passwordConfirmation: 'password123',
      );

      final json = request.toJson();

      expect(json['name'], '<PERSON>');
      expect(json['email'], '<EMAIL>');
      expect(json['password'], 'password123');
      expect(json['password_confirmation'], 'password123');
    });

    test('LoginResponse should deserialize from JSON correctly', () {
      final json = {
        'success': true,
        'message': 'Login success.',
        'token': 'abc123token',
      };

      final response = LoginResponse.fromJson(json);

      expect(response.success, true);
      expect(response.message, 'Login success.');
      expect(response.token, 'abc123token');
    });

    test('RegisterResponse should deserialize from JSON correctly', () {
      final json = {
        'message': 'User registered successfully',
        'user': 'user_data_string',
        'token': 'abc123token',
      };

      final response = RegisterResponse.fromJson(json);

      expect(response.message, 'User registered successfully');
      expect(response.user, 'user_data_string');
      expect(response.token, 'abc123token');
    });
  });
}
