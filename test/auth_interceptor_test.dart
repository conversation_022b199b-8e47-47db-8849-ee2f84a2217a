import 'package:dio/dio.dart';
import 'package:e_library/features/auth/data/auth_interceptor.dart';
import 'package:e_library/features/auth/data/auth_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('AuthInterceptor', () {
    late AuthInterceptor interceptor;
    late Dio dio;

    setUp(() {
      interceptor = AuthInterceptor();
      dio = Dio();
      dio.interceptors.add(interceptor);
      
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    test('should handle 401 error and clear token', () async {
      // Set up a token first
      await AuthService.saveToken('test-token');
      
      // Verify token is saved
      final savedToken = await AuthService.getToken();
      expect(savedToken, equals('test-token'));

      // Create a 401 error
      final requestOptions = RequestOptions(path: '/test');
      final response = Response(
        requestOptions: requestOptions,
        statusCode: 401,
        data: {'message': 'Unauthorized'},
      );
      
      final dioError = DioException(
        requestOptions: requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
      );

      // Simulate the interceptor handling the error
      bool errorHandled = false;
      final handler = _MockErrorInterceptorHandler((error) async {
        errorHandled = true;
        expect(error.message, contains('Session expired'));

        // Verify token was cleared after the interceptor runs
        final clearedToken = await AuthService.getToken();
        expect(clearedToken, isNull);
      });

      interceptor.onError(dioError, handler);

      // Wait a bit for async operations to complete
      await Future.delayed(const Duration(milliseconds: 100));
      expect(errorHandled, isTrue);
    });

    test('should not affect non-401 errors', () async {
      // Set up a token first
      await AuthService.saveToken('test-token');

      // Create a 500 error
      final requestOptions = RequestOptions(path: '/test');
      final response = Response(
        requestOptions: requestOptions,
        statusCode: 500,
        data: {'message': 'Server Error'},
      );
      
      final dioError = DioException(
        requestOptions: requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
      );

      // Simulate the interceptor handling the error
      bool errorHandled = false;
      final handler = _MockErrorInterceptorHandler((error) async {
        errorHandled = true;
        expect(error.response?.statusCode, equals(500));
      });

      interceptor.onError(dioError, handler);

      // Wait a bit for async operations to complete
      await Future.delayed(const Duration(milliseconds: 100));

      // Verify token was NOT cleared
      final token = await AuthService.getToken();
      expect(token, equals('test-token'));
      expect(errorHandled, isTrue);
    });
  });
}

class _MockErrorInterceptorHandler extends ErrorInterceptorHandler {
  final Function(DioException) onNext;

  _MockErrorInterceptorHandler(this.onNext);

  @override
  void next(DioException err) {
    onNext(err);
  }
}
