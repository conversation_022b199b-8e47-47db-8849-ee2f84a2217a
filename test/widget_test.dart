// E-Library app basic widget test
//
// This test verifies basic app structure and components.

import 'package:e_library/features/auth/ui/screens/login_screen.dart';
import 'package:e_library/features/auth/ui/screens/register_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('Login screen should render correctly', (WidgetTester tester) async {
    // Build the login screen directly
    await tester.pumpWidget(
      MaterialApp(
        home: const LoginScreen(),
      ),
    );

    // Verify that the login screen is shown
    expect(find.text('Welcome Back!'), findsOneWidget);
    expect(find.text('Sign in to your account'), findsOneWidget);

    // Verify login form elements are present
    expect(find.text('Email'), findsOneWidget);
    expect(find.text('Password'), findsOneWidget);
    expect(find.byType(ElevatedButton), findsOneWidget);

    // Verify navigation to register screen link
    expect(find.text("Don't have an account? "), findsOneWidget);
    expect(find.text('Sign Up'), findsOneWidget);
  });

  testWidgets('Register screen should render correctly', (WidgetTester tester) async {
    // Build the register screen directly
    await tester.pumpWidget(
      MaterialApp(
        home: const RegisterScreen(),
      ),
    );

    // Verify that the register screen is shown
    expect(find.text('Create Account'), findsNWidgets(2)); // Title and button
    expect(find.text('Sign up to get started'), findsOneWidget);

    // Verify register form elements are present
    expect(find.text('Full Name'), findsOneWidget);
    expect(find.text('Email'), findsOneWidget);
    expect(find.text('Password'), findsOneWidget);
    expect(find.text('Confirm Password'), findsOneWidget);
    expect(find.byType(ElevatedButton), findsOneWidget);

    // Verify navigation to login screen link
    expect(find.text("Already have an account? "), findsOneWidget);
    expect(find.text('Sign In'), findsOneWidget);
  });
}
