import 'package:flutter/material.dart';

/// Global navigation service for handling navigation without context
/// 
/// This service allows navigation from anywhere in the app, including
/// from interceptors, services, and other non-widget classes.
class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Navigate to a named route
  static Future<dynamic> navigateTo(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamed(routeName, arguments: arguments);
  }

  /// Replace current route with a named route
  static Future<dynamic> navigateToAndReplace(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushReplacementNamed(routeName, arguments: arguments);
  }

  /// Clear all routes and navigate to a named route
  static Future<dynamic> navigateToAndClearStack(String routeName, {Object? arguments}) {
    return navigatorKey.currentState!.pushNamedAndRemoveUntil(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  /// Go back to previous route
  static void goBack() {
    return navigatorKey.currentState!.pop();
  }

  /// Check if we can go back
  static bool canGoBack() {
    return navigatorKey.currentState!.canPop();
  }

  /// Get current context (use with caution)
  static BuildContext? get currentContext => navigatorKey.currentContext;
}
