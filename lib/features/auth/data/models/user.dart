import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String name;
  final String email;
  @<PERSON><PERSON><PERSON>ey(name: 'email_verified_at')
  final String? emailVerifiedAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String updatedAt;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.emailVerifiedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a User from JSON data
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  /// Converts this User to JSON
  Map<String, dynamic> toJson() => _$UserToJson(this);
}
