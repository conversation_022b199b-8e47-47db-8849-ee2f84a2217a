import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'auth_response.g.dart';

@JsonSerializable()
class LoginResponse {
  final bool success;
  final String message;
  final String token;

  const LoginResponse({
    required this.success,
    required this.message,
    required this.token,
  });

  /// Creates a LoginResponse from JSON data
  factory LoginResponse.fromJson(Map<String, dynamic> json) => _$LoginResponseFromJson(json);

  /// Converts this LoginResponse to JSON
  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}

@JsonSerializable()
class RegisterResponse {
  final String message;
  final User user;
  final String token;

  const RegisterResponse({
    required this.message,
    required this.user,
    required this.token,
  });

  /// Creates a RegisterResponse from JSON data
  factory RegisterResponse.fromJson(Map<String, dynamic> json) => _$RegisterResponseFromJson(json);

  /// Converts this RegisterResponse to JSON
  Map<String, dynamic> toJson() => _$RegisterResponseToJson(this);
}

@JsonSerializable()
class LogoutResponse {
  final bool success;
  final String message;

  const LogoutResponse({
    required this.success,
    required this.message,
  });

  /// Creates a LogoutResponse from JSON data
  factory LogoutResponse.fromJson(Map<String, dynamic> json) => _$LogoutResponseFromJson(json);

  /// Converts this LogoutResponse to JSON
  Map<String, dynamic> toJson() => _$LogoutResponseToJson(this);
}
