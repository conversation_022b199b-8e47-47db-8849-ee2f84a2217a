import 'package:dio/dio.dart';
import 'package:e_library/features/auth/data/auth_service.dart';

/// Dio interceptor that handles authentication-related responses
/// 
/// This interceptor automatically:
/// - Removes stored tokens when receiving 401 (Unauthorized) responses
/// - Triggers optional callbacks for UI navigation
/// - Logs authentication-related events
class AuthInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    // Continue with normal response handling
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    // Check if the error is a 401 Unauthorized response
    if (err.response?.statusCode == 401) {
      // Clear authentication data and trigger navigation
      await AuthService.handleUnauthorized();

      // Create a more user-friendly error message
      final modifiedError = DioException(
        requestOptions: err.requestOptions,
        response: err.response,
        type: err.type,
        error: 'Session expired. Please log in again.',
        message: 'Session expired. Please log in again.',
      );

      handler.next(modifiedError);
    } else {
      // Continue with normal error handling for non-401 errors
      handler.next(err);
    }
  }
}
