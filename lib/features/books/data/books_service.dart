import 'package:e_library/features/api/responses/paginated_response.dart';
import 'package:e_library/features/books/data/api.dart';
import 'package:e_library/features/books/data/models/book.dart';

/// Service that provides books data from API
class BooksService {

  // BOOKS

  /// Get paginated books (includes nested author/publisher from API)
  static Future<PaginatedResponse<List<Book>>> getPaginatedBooks({int page = 1}) async {
    return await BooksApi.getPaginatedBooks(page: page);
  }

  /// Get all books (includes nested author/publisher from API)
  static Future<List<Book>> getAllBooks() async {
    return await BooksApi.getAllBooks();
  }

  /// Search books by title with pagination
  static Future<PaginatedResponse<List<Book>>> searchBooks({
    required String query,
    int page = 1,
  }) async {
    return await BooksApi.searchBooks(query: query, page: page);
  }

  /// Get a specific book by ID
  static Future<Book> getBook(int id) async {
    return await BooksApi.getBook(id);
  }

  /// Create a new book
  static Future<Book> createBook({
    required String title,
    required String category,
    required double price,
    required int publisherId,
    required int authorId,
  }) async {
    return await BooksApi.createBook(
      title: title,
      category: category,
      price: price,
      publisherId: publisherId,
      authorId: authorId,
    );
  }

  /// Update an existing book
  static Future<Book> updateBook(
    int id, {
    required String title,
    required String category,
    required double price,
    required int publisherId,
    required int authorId,
  }) async {
    return await BooksApi.updateBook(
      id,
      title: title,
      category: category,
      price: price,
      publisherId: publisherId,
      authorId: authorId,
    );
  }

  /// Delete a book
  static Future<void> deleteBook(int id) async {
    return await BooksApi.deleteBook(id);
  }

  // CATEGORIES

  /// Get all unique book categories
  static Future<List<String>> getCategories() async {
    final books = await BooksApi.getAllBooks();
    return books.map((book) => book.category).toSet().toList()..sort();
  }
}
