// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'book.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Book _$BookFromJson(Map<String, dynamic> json) => Book(
  id: (json['id'] as num).toInt(),
  title: json['title'] as String,
  category: json['category'] as String,
  price: (json['price'] as num).toDouble(),
  publisherId: (json['publisher_id'] as num).toInt(),
  authorId: (json['author_id'] as num).toInt(),
  createdAt: json['created_at'] as String?,
  updatedAt: json['updated_at'] as String?,
  author: json['author'] == null
      ? null
      : Author.fromJson(json['author'] as Map<String, dynamic>),
  publisher: json['publisher'] == null
      ? null
      : Publisher.fromJson(json['publisher'] as Map<String, dynamic>),
);

Map<String, dynamic> _$BookToJson(Book instance) => <String, dynamic>{
  'id': instance.id,
  'title': instance.title,
  'category': instance.category,
  'price': instance.price,
  'publisher_id': instance.publisherId,
  'author_id': instance.authorId,
  'created_at': instance.createdAt,
  'updated_at': instance.updatedAt,
  'author': instance.author,
  'publisher': instance.publisher,
};
