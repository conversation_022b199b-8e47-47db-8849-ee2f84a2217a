import 'package:json_annotation/json_annotation.dart';

part 'author.g.dart';

@JsonSerializable()
class Author {
  final int id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name')
  final String firstName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String lastName;

  final String country;
  final String? city;
  final String? address;

  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;

  @J<PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;

  const Author({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.country,
    this.city,
    this.address,
    this.createdAt,
    this.updatedAt,
  });

  /// Creates an Author from JSON data
  factory Author.fromJson(Map<String, dynamic> json) => _$AuthorFromJson(json);

  /// Converts this Author to JSON
  Map<String, dynamic> toJson() => _$AuthorToJson(this);

  /// Get the full name of the author
  String get fullName => '$firstName $lastName';

  /// Get a display string for the author with location
  String get displayName => '$fullName ($country)';

  /// Get the full location string (city, country)
  String get fullLocation {
    if (city != null && city!.isNotEmpty) {
      return '$city, $country';
    }
    return country;
  }

  /// Get a display string with full location
  String get displayNameWithLocation => '$fullName ($fullLocation)';
}
