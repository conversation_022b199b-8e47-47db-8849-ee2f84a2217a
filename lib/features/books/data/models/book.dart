import 'package:json_annotation/json_annotation.dart';
import 'author.dart';
import 'publisher.dart';

part 'book.g.dart';

@JsonSerializable()
class Book {
  final int id;
  final String title;
  final String category;
  final double price;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'publisher_id')
  final int publisherId;

  @<PERSON><PERSON><PERSON>ey(name: 'author_id')
  final int authorId;

  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;

  @Json<PERSON>ey(name: 'updated_at')
  final String? updatedAt;

  // Nested objects from API
  final Author? author;
  final Publisher? publisher;

  const Book({
    required this.id,
    required this.title,
    required this.category,
    required this.price,
    required this.publisherId,
    required this.authorId,
    this.createdAt,
    this.updatedAt,
    this.author,
    this.publisher,
  });

  /// Creates a Book from JSON data
  factory Book.fromJson(Map<String, dynamic> json) => _$BookFromJson(json);

  /// Converts this Book to JSON
  Map<String, dynamic> toJson() => _$BookToJson(this);

  String get formattedPrice {
    return '\$${price.toStringAsFixed(2)}';
  }

  /// Get author name from nested object or fallback
  String get authorName => author?.fullName ?? 'Unknown Author';

  /// Get publisher name from nested object or fallback
  String get publisherName => publisher?.name ?? 'Unknown Publisher';
}
