import 'package:json_annotation/json_annotation.dart';

part 'publisher.g.dart';

@JsonSerializable()
class Publisher {
  final int id;
  final String name;
  final String? city;

  @JsonKey(name: 'created_at')
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  const Publisher({
    required this.id,
    required this.name,
    this.city,
    this.createdAt,
    this.updatedAt,
  });

  /// Creates a Publisher from JSON data
  factory Publisher.fromJson(Map<String, dynamic> json) => _$PublisherFromJson(json);

  /// Converts this Publisher to JSON
  Map<String, dynamic> toJson() => _$PublisherToJson(this);

  /// Get a display string for the publisher
  String get displayName => name;

  /// Get a display string with location
  String get displayNameWithLocation {
    if (city != null && city!.isNotEmpty) {
      return '$name ($city)';
    }
    return name;
  }
}
