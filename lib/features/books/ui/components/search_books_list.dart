import 'dart:async';

import 'package:e_library/features/books/data/books_service.dart';
import 'package:e_library/features/books/data/models/book.dart';
import 'package:e_library/features/books/ui/components/books_list.dart';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

class SearchBooksList extends StatefulWidget {
  final String searchQuery;
  final VoidCallback? onEmptyResults;

  const SearchBooksList({
    super.key,
    required this.searchQuery,
    this.onEmptyResults,
  });

  @override
  State<SearchBooksList> createState() => _SearchBooksListState();
}

class _SearchBooksListState extends State<SearchBooksList> {
  final PagingController<int, Book> _pagingController = PagingController(
    firstPageKey: 1,
  );

  String _currentQuery = '';
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _currentQuery = widget.searchQuery;
    _pagingController.addPageRequestListener((pageKey) {
      _fetchPage(pageKey);
    });
    
    // Initial search if query is not empty
    if (_currentQuery.isNotEmpty) {
      _performSearch();
    }
  }

  @override
  void didUpdateWidget(SearchBooksList oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if search query changed
    if (widget.searchQuery != oldWidget.searchQuery) {
      _updateSearchQuery(widget.searchQuery);
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _pagingController.dispose();
    super.dispose();
  }

  void _updateSearchQuery(String newQuery) {
    // Cancel any existing debounce timer
    _debounceTimer?.cancel();

    // Update current query immediately for UI consistency
    _currentQuery = newQuery.trim();

    // If query is empty, clear results immediately
    if (_currentQuery.isEmpty) {
      _pagingController.refresh();
      return;
    }

    // Debounce the search for non-empty queries
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch();
    });
  }

  void _performSearch() {
    _pagingController.refresh();
  }

  Future<void> _fetchPage(int pageKey) async {
    try {
      // If no search query, return empty results
      if (_currentQuery.trim().isEmpty) {
        _pagingController.appendLastPage([]);
        return;
      }

      final response = await BooksService.searchBooks(
        query: _currentQuery,
        page: pageKey,
      );
      
      final books = response.data;
      final isLastPage = pageKey >= response.meta.lastPage;

      if (isLastPage) {
        _pagingController.appendLastPage(books);
      } else {
        final nextPageKey = pageKey + 1;
        _pagingController.appendPage(books, nextPageKey);
      }

      // Notify parent if no results found on first page
      if (pageKey == 1 && books.isEmpty && widget.onEmptyResults != null) {
        widget.onEmptyResults!();
      }
    } catch (error) {
      _pagingController.error = error;
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () => Future.sync(() => _pagingController.refresh()),
      child: PagedListView<int, Book>(
        pagingController: _pagingController,
        builderDelegate: PagedChildBuilderDelegate<Book>(
          itemBuilder: (context, book, index) => BookListItem(book: book),
          firstPageErrorIndicatorBuilder: (context) => _buildErrorIndicator(
            error: _pagingController.error,
            onRetry: () => _pagingController.refresh(),
          ),
          newPageErrorIndicatorBuilder: (context) =>
              _buildNewPageErrorIndicator(),
          firstPageProgressIndicatorBuilder: (context) =>
              _buildFirstPageLoadingIndicator(),
          newPageProgressIndicatorBuilder: (context) =>
              _buildNewPageLoadingIndicator(),
          noItemsFoundIndicatorBuilder: (context) => _buildNoResultsIndicator(),
          noMoreItemsIndicatorBuilder: (context) =>
              _buildNoMoreItemsIndicator(),
        ),
      ),
    );
  }

  Widget _buildErrorIndicator({
    required dynamic error,
    required VoidCallback onRetry,
  }) {
    return Container(
      padding: const EdgeInsets.all(32),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Theme.of(context).colorScheme.error,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to search books',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: onRetry,
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildNewPageErrorIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: Column(
        children: [
          Text(
            'Failed to load more results',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          TextButton.icon(
            onPressed: () => _pagingController.retryLastFailedRequest(),
            icon: const Icon(Icons.refresh, size: 16),
            label: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildFirstPageLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(32),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(
            'Searching books...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewPageLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(),
    );
  }

  Widget _buildNoResultsIndicator() {
    return Container(
      padding: const EdgeInsets.all(32),
      alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            color: Theme.of(context).colorScheme.outline,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'No books found',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _currentQuery.isNotEmpty
                ? 'Try searching with different keywords'
                : 'Enter a search term to find books',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoMoreItemsIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: Column(
        children: [
          Icon(
            Icons.check_circle_outline,
            color: Theme.of(context).colorScheme.outline,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            'End of search results',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.outline,
            ),
          ),
        ],
      ),
    );
  }
}
