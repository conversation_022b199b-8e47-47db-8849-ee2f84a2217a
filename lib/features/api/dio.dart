import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:e_library/features/auth/data/auth_interceptor.dart';

final Dio dio = _createDio();

Dio _createDio() {
  final dioInstance = Dio(
    BaseOptions(
      baseUrl: 'http://e-library.test/api/',
      headers: {Headers.acceptHeader: 'application/json'},
    ),
  );

  dioInstance.interceptors.add(AuthInterceptor());

  dioInstance.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));

  return dioInstance;
}
