import 'package:json_annotation/json_annotation.dart';

import 'api_response.dart';

part 'paginated_response.g.dart';

/// Paginated API response that extends the base API response.
///
/// This class adds pagination-related fields to the base ApiResponse.
/// Since json_serializable doesn't directly support generic types,
/// we need to implement the fromJson and toJson methods manually.
class PaginatedResponse<T> extends ApiResponse<T> {
  final PaginationLinks links;
  final PaginationMeta meta;

  PaginatedResponse({
    required super.data,
    required this.links,
    required this.meta,
  });

  /// Creates an instance from JSON using the provided fromJson function.
  ///
  /// This factory method requires a function that can convert the JSON data
  /// to the appropriate type T.
  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) {
    return PaginatedResponse<T>(
      data: fromJsonT(json['data']),
      links: PaginationLinks.fromJson(json['links'] as Map<String, dynamic>),
      meta: PaginationMeta.fromJson(json['meta'] as Map<String, dynamic>),
    );
  }

  /// Creates an instance from JSON for a list of items.
  ///
  /// This factory method is specifically for when T is a List<S>.
  /// It requires a function that can convert each item in the JSON data list
  /// to the appropriate type S.
  static PaginatedResponse<List<S>> fromJsonList<S>(
    Map<String, dynamic> json,
    S Function(Map<String, dynamic> json) fromJsonS,
  ) {
    final List<dynamic> dataList = json['data'] as List<dynamic>;
    return PaginatedResponse<List<S>>(
      data: dataList
          .map((item) => fromJsonS(item as Map<String, dynamic>))
          .toList(),
      links: PaginationLinks.fromJson(json['links'] as Map<String, dynamic>),
      meta: PaginationMeta.fromJson(json['meta'] as Map<String, dynamic>),
    );
  }

  /// Converts this object to a JSON map.
  ///
  /// This method requires a function that can convert the data of type T
  /// to a JSON-compatible object.
  @override
  Map<String, dynamic> toJson(Object? Function(T data) toJsonT) {
    final baseJson = super.toJson(toJsonT);
    return {...baseJson, 'links': links.toJson(), 'meta': meta.toJson()};
  }

  static PaginatedResponse<List<T>> empty<T>() {
    return PaginatedResponse<List<T>>(
      data: [],
      links: PaginationLinks(first: null, last: null, prev: null, next: null),
      meta: PaginationMeta(
        currentPage: 1,
        from: 0,
        lastPage: 1,
        links: [],
        path: '',
        perPage: 0,
        to: 0,
        total: 0,
      ),
    );
  }
}

/// Links for pagination navigation.
@JsonSerializable()
class PaginationLinks {
  final String? first;
  final String? last;
  final String? prev;
  final String? next;

  PaginationLinks({this.first, this.last, this.prev, this.next});

  factory PaginationLinks.fromJson(Map<String, dynamic> json) =>
      _$PaginationLinksFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationLinksToJson(this);
}

/// Metadata for pagination.
@JsonSerializable()
class PaginationMeta {
  @JsonKey(name: 'current_page')
  final int currentPage;
  final int? from;
  @JsonKey(name: 'last_page')
  final int lastPage;
  final List<PaginationMetaLink> links;
  final String path;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int? to;
  final int total;

  PaginationMeta({
    required this.currentPage,
    this.from,
    required this.lastPage,
    required this.links,
    required this.path,
    required this.perPage,
    this.to,
    required this.total,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}

/// Link item in pagination metadata.
@JsonSerializable()
class PaginationMetaLink {
  final String? url;
  final String label;
  final bool active;

  PaginationMetaLink({this.url, required this.label, required this.active});

  factory PaginationMetaLink.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaLinkFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaLinkToJson(this);
}
